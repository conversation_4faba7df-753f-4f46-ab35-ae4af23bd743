import axios from 'axios';
import * as fs from 'fs-extra';
import * as path from 'path';
import { EventEmitter } from 'events';
import { 
  M3U8Playlist, 
  M3U8Segment, 
  CourseResource, 
  DownloadProgress,
  M3U8Error,
  NetworkError,
  FileError 
} from '../types';
import { M3U8Parser } from './M3U8Parser';
import { VideoMerger, MergeProgress } from './VideoMerger';
import { RetryManager } from './RetryManager';
import { FileOrganizer } from './FileOrganizer';

/**
 * M3U8下载器配置
 */
export interface M3U8DownloaderConfig {
  maxConcurrentDownloads: number;
  segmentTimeout: number;
  maxRetries: number;
  retryDelay: number;
  tempDir: string;
  userAgent: string;
  deleteSegmentsAfterMerge: boolean;
}

/**
 * 下载状态
 */
export interface M3U8DownloadStatus {
  phase: 'parsing' | 'downloading' | 'merging' | 'completed' | 'failed';
  totalSegments: number;
  downloadedSegments: number;
  failedSegments: number;
  currentSegment?: number;
  progress: number; // 0-100
  speed: number; // bytes per second
  estimatedTime: number; // seconds
  error?: string;
}

/**
 * M3U8视频下载器
 * 负责下载M3U8视频流并合并为MP4文件
 */
export class M3U8Downloader extends EventEmitter {
  private config: M3U8DownloaderConfig;
  private parser: M3U8Parser;
  private merger: VideoMerger;
  private retryManager: RetryManager;
  private fileOrganizer: FileOrganizer;
  
  private activeDownloads: Map<string, AbortController> = new Map();
  private downloadStats: Map<string, { startTime: number; downloadedBytes: number }> = new Map();

  private readonly defaultConfig: M3U8DownloaderConfig = {
    maxConcurrentDownloads: 5,
    segmentTimeout: 30000, // 30 seconds
    maxRetries: 3,
    retryDelay: 1000, // 1 second
    tempDir: path.join(process.cwd(), 'temp'),
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    deleteSegmentsAfterMerge: true
  };

  constructor(
    fileOrganizer: FileOrganizer,
    config?: Partial<M3U8DownloaderConfig>
  ) {
    super();
    this.config = { ...this.defaultConfig, ...config };
    this.fileOrganizer = fileOrganizer;
    
    // 初始化组件
    this.parser = new M3U8Parser({
      timeout: this.config.segmentTimeout,
      maxRetries: this.config.maxRetries,
      retryDelay: this.config.retryDelay,
      userAgent: this.config.userAgent
    });

    this.merger = new VideoMerger({
      tempDir: this.config.tempDir,
      deleteSegments: this.config.deleteSegmentsAfterMerge
    });

    this.retryManager = new RetryManager({
      maxRetries: this.config.maxRetries,
      baseDelay: this.config.retryDelay,
      strategy: 'exponential'
    });

    // 设置事件监听
    this.setupEventListeners();
  }

  /**
   * 下载M3U8视频
   * @param resource 视频资源
   * @param onProgress 进度回调
   */
  async downloadVideo(
    resource: CourseResource,
    onProgress?: (progress: DownloadProgress) => void
  ): Promise<string> {
    const taskId = this.generateTaskId(resource);
    
    try {
      // 检查资源类型
      if (resource.type !== 'video') {
        throw new M3U8Error('资源类型不是视频');
      }

      // 验证M3U8 URL
      if (!M3U8Parser.isM3U8Url(resource.url)) {
        throw new M3U8Error('不是有效的M3U8 URL');
      }

      // 生成输出路径
      const outputPath = this.fileOrganizer.generatePath(resource);
      await fs.ensureDir(path.dirname(outputPath));

      // 检查文件是否已存在
      if (await fs.pathExists(outputPath)) {
        const shouldOverwrite = await this.checkFileOverwrite(outputPath, resource);
        if (!shouldOverwrite) {
          return outputPath;
        }
      }

      // 创建下载状态
      const status: M3U8DownloadStatus = {
        phase: 'parsing',
        totalSegments: 0,
        downloadedSegments: 0,
        failedSegments: 0,
        progress: 0,
        speed: 0,
        estimatedTime: 0
      };

      this.emit('status', taskId, status);

      // 解析M3U8播放列表
      const playlist = await this.parser.parsePlaylist(resource.url);
      
      status.phase = 'downloading';
      status.totalSegments = playlist.segments.length;
      this.emit('status', taskId, status);

      // 创建临时目录
      const tempDir = await this.createTempDir(taskId);
      
      // 下载所有片段
      const segmentPaths = await this.downloadSegments(
        playlist, 
        tempDir, 
        taskId,
        (progress) => {
          status.downloadedSegments = progress.completedSegments;
          status.failedSegments = progress.failedSegments;
          status.progress = Math.round((progress.completedSegments / status.totalSegments) * 80); // 80% for download
          status.speed = progress.speed;
          status.estimatedTime = progress.estimatedTime;
          status.currentSegment = progress.currentSegment;
          
          this.emit('status', taskId, status);
          
          if (onProgress) {
            onProgress({
              progress: status.progress,
              speed: status.speed,
              estimatedTime: status.estimatedTime
            });
          }
        }
      );

      // 合并视频片段
      status.phase = 'merging';
      status.progress = 80;
      this.emit('status', taskId, status);

      await this.mergeVideoSegments(segmentPaths, outputPath, (mergeProgress) => {
        status.progress = 80 + Math.round(mergeProgress.progress * 0.2); // 20% for merge
        this.emit('status', taskId, status);
        
        if (onProgress) {
          onProgress({
            progress: status.progress,
            speed: 0,
            estimatedTime: 0
          });
        }
      });

      // 清理临时文件
      await this.cleanupTempDir(tempDir);

      // 完成
      status.phase = 'completed';
      status.progress = 100;
      this.emit('status', taskId, status);
      
      if (onProgress) {
        onProgress({
          progress: 100,
          speed: 0,
          estimatedTime: 0
        });
      }

      return outputPath;
    } catch (error) {
      const status: M3U8DownloadStatus = {
        phase: 'failed',
        totalSegments: 0,
        downloadedSegments: 0,
        failedSegments: 0,
        progress: 0,
        speed: 0,
        estimatedTime: 0,
        error: error instanceof Error ? error.message : '未知错误'
      };
      
      this.emit('status', taskId, status);
      throw error;
    } finally {
      // 清理活动下载
      this.activeDownloads.delete(taskId);
      this.downloadStats.delete(taskId);
    }
  }

  /**
   * 下载视频片段
   */
  private async downloadSegments(
    playlist: M3U8Playlist,
    tempDir: string,
    taskId: string,
    onProgress: (progress: {
      completedSegments: number;
      failedSegments: number;
      currentSegment: number;
      speed: number;
      estimatedTime: number;
    }) => void
  ): Promise<string[]> {
    const segments = playlist.segments;
    const segmentPaths: string[] = [];
    let completedSegments = 0;
    let failedSegments = 0;
    const startTime = Date.now();
    let totalDownloadedBytes = 0;

    // 创建并发下载队列
    const downloadQueue = segments.map((segment, index) => ({
      segment,
      index,
      outputPath: path.join(tempDir, `segment_${String(index).padStart(6, '0')}.ts`)
    }));

    // 并发下载控制
    const concurrentDownloads = Math.min(this.config.maxConcurrentDownloads, segments.length);
    const downloadPromises: Promise<void>[] = [];

    for (let i = 0; i < concurrentDownloads; i++) {
      downloadPromises.push(this.downloadWorker(downloadQueue, segmentPaths, (bytes) => {
        totalDownloadedBytes += bytes;
        completedSegments++;
        
        const elapsedTime = (Date.now() - startTime) / 1000;
        const speed = totalDownloadedBytes / elapsedTime;
        const remainingSegments = segments.length - completedSegments;
        const estimatedTime = remainingSegments > 0 ? (remainingSegments * elapsedTime) / completedSegments : 0;

        onProgress({
          completedSegments,
          failedSegments,
          currentSegment: completedSegments,
          speed,
          estimatedTime
        });
      }));
    }

    await Promise.all(downloadPromises);

    if (failedSegments > 0) {
      throw new M3U8Error(`${failedSegments} 个视频片段下载失败`);
    }

    return segmentPaths;
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(resource: CourseResource): string {
    return `m3u8_${resource.id}_${Date.now()}`;
  }

  /**
   * 创建临时目录
   */
  private async createTempDir(taskId: string): Promise<string> {
    const tempDir = path.join(this.config.tempDir, taskId);
    await fs.ensureDir(tempDir);
    return tempDir;
  }

  /**
   * 清理临时目录
   */
  private async cleanupTempDir(tempDir: string): Promise<void> {
    try {
      if (await fs.pathExists(tempDir)) {
        await fs.remove(tempDir);
      }
    } catch (error) {
      console.warn(`清理临时目录失败: ${tempDir}`, error);
    }
  }

  /**
   * 检查文件覆盖
   */
  private async checkFileOverwrite(filePath: string, resource: CourseResource): Promise<boolean> {
    // 简单实现：总是覆盖，实际应用中可以检查文件大小、修改时间等
    return true;
  }

  /**
   * 取消下载
   */
  cancelDownload(taskId: string): void {
    const controller = this.activeDownloads.get(taskId);
    if (controller) {
      controller.abort();
      this.activeDownloads.delete(taskId);
    }
  }

  /**
   * 下载工作器
   */
  private async downloadWorker(
    queue: Array<{ segment: M3U8Segment; index: number; outputPath: string }>,
    segmentPaths: string[],
    onSegmentComplete: (bytes: number) => void
  ): Promise<void> {
    while (queue.length > 0) {
      const item = queue.shift();
      if (!item) break;

      try {
        const downloadedBytes = await this.downloadSegment(item.segment, item.outputPath);
        segmentPaths[item.index] = item.outputPath;
        onSegmentComplete(downloadedBytes);
      } catch (error) {
        console.error(`下载片段失败: ${item.segment.url}`, error);
        throw error;
      }
    }
  }

  /**
   * 下载单个视频片段
   */
  private async downloadSegment(segment: M3U8Segment, outputPath: string): Promise<number> {
    return await this.retryManager.executeWithRetry(async () => {
      const response = await axios.get(segment.url, {
        timeout: this.config.segmentTimeout,
        responseType: 'stream',
        headers: {
          'User-Agent': this.config.userAgent
        }
      });

      if (response.status !== 200) {
        throw new NetworkError(`HTTP ${response.status}: ${response.statusText}`);
      }

      const writer = fs.createWriteStream(outputPath);
      response.data.pipe(writer);

      return new Promise<number>((resolve, reject) => {
        let downloadedBytes = 0;

        response.data.on('data', (chunk: Buffer) => {
          downloadedBytes += chunk.length;
        });

        writer.on('finish', () => resolve(downloadedBytes));
        writer.on('error', reject);
        response.data.on('error', reject);
      });
    });
  }

  /**
   * 合并视频片段
   */
  private async mergeVideoSegments(
    segmentPaths: string[],
    outputPath: string,
    onProgress: (progress: MergeProgress) => void
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      this.merger.on('progress', onProgress);
      this.merger.on('completed', resolve);
      this.merger.on('error', reject);

      this.merger.mergeSegments(segmentPaths, outputPath, {
        outputPath,
        format: 'mp4',
        deleteSegments: this.config.deleteSegmentsAfterMerge
      });
    });
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 可以在这里添加全局事件监听器
  }
}

export default M3U8Downloader;
